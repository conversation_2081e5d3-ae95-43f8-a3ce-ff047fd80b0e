# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a comprehensive machine learning pipeline for predicting and imputing missing well log data using advanced algorithms including gradient boosting models, deep learning approaches, and traditional statistical methods. The project features GPU acceleration, advanced preprocessing, and comprehensive visualization capabilities.

## Environment Setup

### Virtual Environment
The project uses a virtual environment located at `C:\Users\<USER>\imputeML\Scripts\activate`. Activate it before running any commands:
```bash
C:\Users\<USER>\imputeML\Scripts\activate
```

### Dependencies
Install required packages from requirements.txt:
```bash
pip install -r requirements.txt
```

### GPU Support
The project supports GPU acceleration with CUDA. Key GPU-accelerated components:
- XGBoost with `device='cuda'`
- PyTorch models for deep learning
- CatBoost with `task_type='GPU'`

## Common Commands

### Running the Main Pipeline
```bash
python main.py
```
This launches the interactive GUI workflow for the complete ML pipeline.

### Code Quality
```bash
# Linting (flake8 is available)
python -m flake8 main.py ml_core.py data_handler.py

# No pytest framework is currently installed
```

### Testing Individual Components
```bash
# Test simple functionality
python simple_test.py

# Test specific model implementations
python test_file.py
```

## Architecture Overview

### Core Pipeline Files
- **`main.py`**: Entry point with interactive GUI workflow orchestrating the complete ML pipeline
- **`data_handler.py`**: LAS file operations, data loading, cleaning, and preprocessing
- **`ml_core.py`**: Machine learning model registry and training pipeline with MODEL_REGISTRY
- **`config_handler.py`**: User interfaces, file selection dialogs, and configuration management
- **`reporting.py`**: Visualization, analysis, and performance reporting

### Model Implementation Structure
- **`models/`**: Contains all ML model implementations
  - `simple_autoencoder.py`: Neural network-based imputation models
  - `advanced_models/`: SAITS, BRITS, Transformer, MRNN implementations
  - `__init__.py`: Package initialization with model registry

### Utility Modules
- **`utils/`**: Specialized functionality
  - `gpu_acceleration.py`: GPU optimization and CUDA operations  
  - `xgboost_gpu_utils.py`: XGBoost-specific GPU configurations
  - `display_utils.py`: Cross-platform display formatting
  - `memory_optimization.py`: Memory management utilities
  - `performance_monitor.py`: Performance tracking and benchmarking

### Data and Configuration
- **`Las/`**: Input LAS (Log ASCII Standard) well log files
- **`config/display_config.ini`**: Display and visualization settings
- **`plots/`**: Generated visualization outputs
- **`requirements.txt`**: Python package dependencies

## Model Categories

### Gradient Boosting Models (GPU-Accelerated)
- **XGBoost**: Modern GPU acceleration with `device='cuda'`
- **LightGBM**: High-performance gradient boosting with `device='gpu'`
- **CatBoost**: Categorical feature handling with `task_type='GPU'`

### Deep Learning Models  
- **Autoencoder**: Neural network-based imputation
- **U-Net**: Advanced sequence-to-sequence learning
- **Advanced Models**: SAITS, BRITS, Transformer, MRNN (from PyPOTS library)

### Statistical Models
- **Linear Regression**: Interpretable baseline with diagnostics
- **Ridge Regression**: L2 regularization for multicollinearity

## Workflow Steps

1. **File Selection**: GUI dialog for LAS file selection
2. **Data Loading**: Automated LAS file processing with error handling
3. **Log Configuration**: Feature and target log selection
4. **Training Strategy**: Well separation and prediction mode configuration
5. **Model Selection**: Multi-model selection from MODEL_REGISTRY
6. **Execution**: Batch model training and evaluation
7. **Analysis**: Performance comparison and ranking
8. **Visualization**: Comprehensive plotting and quality control
9. **Output**: Results export to LAS files and reports

## Key Features

### Multi-Model Comparison
- Batch execution of multiple models simultaneously
- Automated performance ranking based on composite scores
- Side-by-side visualization comparisons
- Statistical evaluation with MAE, R², RMSE metrics

### Advanced Data Processing
- Smart data cleaning with domain-specific rules for well log data
- Enhanced preprocessing with outlier detection
- Data leakage detection for model validation
- Sequence creation for deep learning models

### Professional Visualization
- Quality control plots with cross-plot analysis
- Model performance dashboards and residual analysis
- Publication-ready charts with customizable styling
- Multi-model comparison visualizations

## Development Notes

### Model Registry
The `MODEL_REGISTRY` in `ml_core.py` contains all available models. New models should be registered here with proper configuration including type ('shallow', 'deep', 'deep_advanced') and model class references.

### GPU Memory Management
The codebase includes automatic GPU detection with CPU fallback strategies. Memory optimization utilities handle large datasets efficiently.

### File Structure Dependencies
Core pipeline execution requires all files in the dependency chain: main.py → config_handler.py → data_handler.py → ml_core.py → reporting.py → models/ → utils/

### Interactive Workflow
The main pipeline provides extensive user interaction through console selections and GUI dialogs for file management, making it suitable for both technical users and domain experts.