"""
Test script for memory-optimized Transformer model
Tests the memory optimization features with the problematic dataset size.
"""

import os
import sys
import torch
import numpy as np
import traceback
from typing import Dict, Any

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_memory_optimization():
    """Test memory optimization with the problematic dataset size."""
    
    print("="*60)
    print("🧪 TESTING MEMORY-OPTIMIZED TRANSFORMER")
    print("="*60)
    
    # Test parameters matching your dataset
    n_samples = 27618
    sequence_len = 64
    n_features = 5
    
    print(f"📊 Test Dataset:")
    print(f"   • Samples: {n_samples:,}")
    print(f"   • Sequence length: {sequence_len}")
    print(f"   • Features: {n_features}")
    print(f"   • Total data points: {n_samples * sequence_len * n_features:,}")
    
    try:
        # Import the memory optimization utilities
        from utils.memory_optimization import (
            get_memory_optimizer, 
            create_memory_report,
            adaptive_batch_size_finder,
            print_comprehensive_memory_report
        )
        
        from utils.gpu_fallback import get_fallback_manager, check_memory_pressure
        
        print("\n✅ Successfully imported memory optimization utilities")
        
        # Initialize memory optimizer
        memory_optimizer = get_memory_optimizer(
            enable_mixed_precision=True,
            enable_monitoring=True
        )
        
        print("\n📊 Initial System Status:")
        memory_optimizer.print_memory_status()
        
        # Check memory pressure
        memory_pressure = check_memory_pressure()
        print(f"\n🌡️ Memory Pressure: {memory_pressure['memory_pressure'].upper()}")
        
        # Test model parameters (Transformer configuration)
        model_params = {
            'd_model': 256,
            'n_layers': 6,
            'n_heads': 8,
            'memory_optimizations': {
                'gradient_checkpointing': True,
                'mixed_precision': True,
                'adaptive_batch_size': True,
                'memory_efficient_mode': True
            }
        }
        
        print(f"\n🤖 Model Configuration:")
        print(f"   • Model dimension: {model_params['d_model']}")
        print(f"   • Layers: {model_params['n_layers']}")
        print(f"   • Attention heads: {model_params['n_heads']}")
        
        # Test adaptive batch size finding
        print(f"\n🔍 Finding optimal batch size...")
        
        try:
            optimal_batch_size = adaptive_batch_size_finder(
                (n_samples, sequence_len, n_features),
                model_params,
                start_batch_size=32,
                min_batch_size=1
            )
            
            print(f"✅ Optimal batch size found: {optimal_batch_size}")
            
        except Exception as e:
            print(f"⚠️ Batch size optimization failed: {e}")
            optimal_batch_size = 4  # Fallback to very small batch
        
        # Generate comprehensive memory report
        print(f"\n📋 Generating comprehensive memory report...")
        print_comprehensive_memory_report(
            (n_samples, sequence_len, n_features),
            model_params,
            optimal_batch_size
        )
        
        # Test creating dummy data and checking memory requirements
        print(f"\n🧪 Testing with synthetic data...")
        
        # Create small test data to validate the approach
        test_samples = min(1000, n_samples)  # Use smaller subset for testing
        test_data = torch.randn(test_samples, sequence_len, n_features)
        
        # Add some NaN values to simulate missing data
        mask = torch.rand(test_samples, sequence_len, n_features) < 0.2  # 20% missing
        test_data[mask] = float('nan')
        
        print(f"   • Test data shape: {test_data.shape}")
        print(f"   • Missing values: {torch.isnan(test_data).sum().item()}")
        
        # Test the actual Transformer model initialization
        print(f"\n🚀 Testing Transformer model initialization...")
        
        try:
            from models.advanced_models.transformer_model import TransformerModel
            
            # Create model with memory optimization enabled
            transformer = TransformerModel(
                n_features=n_features,
                sequence_len=sequence_len,
                d_model=128,  # Reduced for testing
                n_heads=4,    # Reduced for testing
                n_encoder_layers=2,  # Reduced for testing
                d_ff=256,     # Reduced for testing
                epochs=5,     # Reduced for testing
                batch_size=optimal_batch_size,
                enable_gradient_checkpointing=True,
                enable_mixed_precision=True,
                adaptive_batch_size=True,
                memory_efficient=True
            )
            
            print("✅ Transformer model created successfully with memory optimizations")
            
            # Get model complexity info
            complexity = transformer.get_model_complexity()
            print(f"\n📊 Model Complexity:")
            print(f"   • Parameters: {complexity['total_parameters']:,}")
            print(f"   • Memory estimate: {complexity['memory_mb']:.1f} MB")
            print(f"   • Memory optimizations: {complexity['memory_optimizations']}")
            
            # Test a small training run (just initialization)
            print(f"\n🏋️ Testing model initialization...")
            
            # Use small subset for testing
            small_test_data = test_data[:min(100, test_samples)]
            small_truth_data = torch.randn_like(small_test_data)  # Dummy truth data
            
            # Just test model initialization, not full training
            transformer._initialize_model()
            
            print("✅ Model initialized successfully!")
            
            # Print final memory status
            print(f"\n📊 Final Memory Status:")
            memory_optimizer.print_memory_status()
            
            # Print fallback report
            fallback_manager = get_fallback_manager()
            fallback_manager.print_fallback_report()
            
            print(f"\n🎉 MEMORY OPTIMIZATION TEST COMPLETED SUCCESSFULLY!")
            print(f"   • The optimized Transformer should now handle your dataset")
            print(f"   • Dataset: {n_samples:,} samples × {sequence_len} × {n_features}")
            print(f"   • Recommended batch size: {optimal_batch_size}")
            print(f"   • Memory optimizations: All enabled")
            
            return True
            
        except Exception as model_e:
            print(f"❌ Transformer model test failed: {model_e}")
            print("📝 Error details:", traceback.format_exc())
            return False
            
    except ImportError as ie:
        print(f"❌ Import error: {ie}")
        print("💡 Make sure you're running from the correct directory")
        return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("📝 Error details:", traceback.format_exc())
        return False

def main():
    """Main test function."""
    print("🚀 Starting Memory Optimization Test")
    print(f"🐍 Python version: {sys.version}")
    print(f"🔥 PyTorch version: {torch.__version__}")
    print(f"🚀 CUDA available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"🎮 GPU: {torch.cuda.get_device_name()}")
        print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / (1024**3):.1f} GB")
    
    success = test_memory_optimization()
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("💡 You can now run your Transformer model with the memory optimizations")
        print("📚 Use the optimized model parameters shown in the report")
    else:
        print("\n❌ TESTS FAILED!")
        print("💡 Check the error messages above for troubleshooting")
    
    return success

if __name__ == "__main__":
    main()