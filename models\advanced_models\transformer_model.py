"""
Transformer Model Implementation for Time Series Imputation
Custom transformer architecture specifically designed for well log data imputation

This module implements a custom transformer model with positional encoding,
multi-head self-attention, and well log specific attention mechanisms.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Dict, Any, Optional, Tuple
import warnings

from .base_model import BaseAdvancedModel

class PositionalEncoding(nn.Module):
    """Positional encoding for transformer model."""
    
    def __init__(self, d_model: int, max_seq_len: int = 512, dropout: float = 0.1):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        # Create positional encoding matrix
        pe = torch.zeros(max_seq_len, d_model)
        position = torch.arange(0, max_seq_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Add positional encoding to input."""
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)

class MultiHeadAttention(nn.Module):
    """Multi-head self-attention mechanism."""
    
    def __init__(self, d_model: int, n_heads: int, dropout: float = 0.1):
        super().__init__()
        assert d_model % n_heads == 0
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass of multi-head attention."""
        batch_size = query.size(0)
        
        # Linear projections
        Q = self.w_q(query).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        
        # Attention
        attention_output, attention_weights = self._attention(Q, K, V, mask)
        
        # Concatenate heads
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, -1, self.d_model)
        
        # Final linear projection
        output = self.w_o(attention_output)
        
        return output, attention_weights
    
    def _attention(self, Q: torch.Tensor, K: torch.Tensor, V: torch.Tensor,
                   mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """Scaled dot-product attention."""
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        output = torch.matmul(attention_weights, V)
        
        return output, attention_weights

class TransformerBlock(nn.Module):
    """Single transformer encoder block."""
    
    def __init__(self, d_model: int, n_heads: int, d_ff: int, dropout: float = 0.1):
        super().__init__()
        self.attention = MultiHeadAttention(d_model, n_heads, dropout)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model)
        )
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Forward pass of transformer block."""
        # Self-attention with residual connection
        attn_output, _ = self.attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # Feed-forward with residual connection
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x

class CustomTransformerNet(nn.Module):
    """Custom transformer network for time series imputation."""
    
    def __init__(self, n_features: int, sequence_len: int, d_model: int = 256,
                 n_heads: int = 8, n_encoder_layers: int = 6, d_ff: int = 1024,
                 dropout: float = 0.1, max_seq_len: int = 512):
        super().__init__()
        
        self.n_features = n_features
        self.sequence_len = sequence_len
        self.d_model = d_model
        
        # Input embedding
        self.input_embedding = nn.Linear(n_features, d_model)
        
        # Positional encoding
        self.pos_encoding = PositionalEncoding(d_model, max_seq_len, dropout)
        
        # Transformer encoder layers
        self.encoder_layers = nn.ModuleList([
            TransformerBlock(d_model, n_heads, d_ff, dropout)
            for _ in range(n_encoder_layers)
        ])
        
        # Output projection
        self.output_projection = nn.Linear(d_model, n_features)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Forward pass of the transformer."""
        # Input embedding
        x = self.input_embedding(x) * math.sqrt(self.d_model)
        
        # Add positional encoding
        x = x.transpose(0, 1)  # (seq_len, batch, d_model)
        x = self.pos_encoding(x)
        x = x.transpose(0, 1)  # (batch, seq_len, d_model)
        
        # Apply transformer encoder layers
        for encoder_layer in self.encoder_layers:
            x = encoder_layer(x, mask)
        
        # Output projection
        output = self.output_projection(x)
        
        return output

class TransformerModel(BaseAdvancedModel):
    """
    Custom Transformer model for well log imputation.
    
    Implements a transformer architecture with positional encoding,
    multi-head self-attention, and well log specific optimizations.
    """
    
    def __init__(self, n_features: int = 4, sequence_len: int = 64, 
                 d_model: int = 256, n_heads: int = 8, n_encoder_layers: int = 6,
                 d_ff: int = 1024, dropout: float = 0.1, max_seq_len: int = 512,
                 epochs: int = 100, batch_size: int = 32, learning_rate: float = 1e-4,
                 use_mixed_precision: bool = True, adaptive_batch_size: bool = True,
                 **kwargs):
        """
        Initialize Transformer model for well log imputation.
        
        Args:
            n_features: Number of log features (GR, NPHI, RHOB, target)
            sequence_len: Length of input sequences (depth windows)
            d_model: Model dimension for attention mechanism
            n_heads: Number of attention heads
            n_encoder_layers: Number of encoder layers
            d_ff: Feed-forward network dimension
            dropout: Dropout rate for regularization
            max_seq_len: Maximum sequence length for positional encoding
            epochs: Training epochs
            batch_size: Training batch size
            learning_rate: Optimizer learning rate
            use_mixed_precision: Enable mixed precision training for memory optimization
            adaptive_batch_size: Enable adaptive batch size calculation
            **kwargs: Additional model parameters
        """
        super().__init__(n_features, sequence_len, epochs, batch_size, learning_rate, **kwargs)
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.n_encoder_layers = n_encoder_layers
        self.d_ff = d_ff
        self.dropout = dropout
        self.max_seq_len = max_seq_len
        
        # Memory optimization settings
        self.use_mixed_precision = use_mixed_precision and torch.cuda.is_available()
        self.adaptive_batch_size = adaptive_batch_size
        self.scaler = torch.amp.GradScaler('cuda') if self.use_mixed_precision else None

        # Import robust mixed precision utilities
        try:
            from utils.mixed_precision_utils import MixedPrecisionTrainer
            self.mp_trainer = None  # Will be initialized when optimizer is created
            self.use_robust_mp = True
        except ImportError:
            print("⚠️ Robust mixed precision utilities not available, using basic implementation")
            self.use_robust_mp = False
        
        # Validate parameters
        self._validate_parameters()
        
        # Training components
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.criterion = nn.MSELoss()
        self.optimizer = None
        
        print(f"Transformer Model Configuration:")
        print(f"   - Model dimension: {d_model}")
        print(f"   - Attention heads: {n_heads}")
        print(f"   - Encoder layers: {n_encoder_layers}")
        print(f"   - Feed-forward dim: {d_ff}")
        print(f"   - Dropout rate: {dropout}")
        print(f"   - Device: {self.device}")
        print(f"   - Mixed precision: {self.use_mixed_precision}")
        print(f"   - Adaptive batch size: {self.adaptive_batch_size}")
        
    def _validate_parameters(self):
        """Validate model parameters for well log data."""
        if self.d_model % self.n_heads != 0:
            raise ValueError(f"d_model ({self.d_model}) must be divisible by n_heads ({self.n_heads})")
        
        if self.sequence_len > self.max_seq_len:
            raise ValueError(f"sequence_len ({self.sequence_len}) cannot exceed max_seq_len ({self.max_seq_len})")
        
        if self.n_features < 2:
            raise ValueError("Transformer requires at least 2 features for meaningful attention")
            
        if self.n_encoder_layers < 1 or self.n_encoder_layers > 12:
            print(f"⚠️ Warning: n_encoder_layers={self.n_encoder_layers} may not be optimal (recommended: 1-12)")
            
        if self.d_model < 64 or self.d_model > 1024:
            print(f"⚠️ Warning: d_model={self.d_model} may not be optimal (recommended: 64-1024)")
    
    def _initialize_weights(self) -> None:
        """Initialize model weights with Xavier/Glorot initialization."""
        for module in self.model.modules():
            if isinstance(module, nn.Linear):
                torch.nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    torch.nn.init.zeros_(module.bias)
            elif isinstance(module, nn.LayerNorm):
                torch.nn.init.ones_(module.weight)
                torch.nn.init.zeros_(module.bias)
    
    def _verify_gradient_setup(self) -> None:
        """Verify that model parameters are properly set up for gradient computation."""
        if self.model is None:
            raise RuntimeError("Model not initialized")
        
        # Check if model is in training mode
        if not self.model.training:
            self.model.train()
            print("Set model to training mode")
        
        # Verify parameters require gradients
        params_requiring_grad = [p for p in self.model.parameters() if p.requires_grad]
        if len(params_requiring_grad) == 0:
            raise RuntimeError("No model parameters require gradients")
        
        # Test a simple forward pass to ensure gradients can be computed
        test_input = torch.randn(1, self.sequence_len, self.n_features, device=self.device, requires_grad=True)
        test_output = self.model(test_input)
        test_loss = test_output.mean()
        
        try:
            test_loss.backward()
            print("Gradient computation verified successfully")
        except Exception as e:
            raise RuntimeError(f"Gradient computation test failed: {e}")
        finally:
            # Clear test gradients
            self.optimizer.zero_grad()
    
    def _initialize_model(self) -> None:
        """Initialize the custom transformer model."""
        try:
            print(f"Initializing Transformer model...")
            
            self.model = CustomTransformerNet(
                n_features=self.n_features,
                sequence_len=self.sequence_len,
                d_model=self.d_model,
                n_heads=self.n_heads,
                n_encoder_layers=self.n_encoder_layers,
                d_ff=self.d_ff,
                dropout=self.dropout,
                max_seq_len=self.max_seq_len
            ).to(self.device)
            
            # Initialize model weights and ensure gradients are enabled
            self._initialize_weights()
            
            # Verify all parameters require gradients
            param_count = 0
            grad_param_count = 0
            for param in self.model.parameters():
                param_count += 1
                if param.requires_grad:
                    grad_param_count += 1
            
            print(f"   - Model parameters: {param_count} total, {grad_param_count} require gradients")
            
            # Initialize optimizer
            self.optimizer = torch.optim.Adam(
                self.model.parameters(),
                lr=self.learning_rate,
                weight_decay=1e-5
            )

            # Initialize robust mixed precision trainer if available
            if self.use_robust_mp:
                from utils.mixed_precision_utils import MixedPrecisionTrainer
                self.mp_trainer = MixedPrecisionTrainer(
                    model=self.model,
                    optimizer=self.optimizer,
                    device=str(self.device),
                    max_grad_norm=1.0,
                    enable_mixed_precision=self.use_mixed_precision
                )
                print("🚀 Robust mixed precision trainer initialized")
            
            print(f"Transformer model initialized successfully")
            print(f"   - Parameters: ~{self._estimate_parameters():,}")
            print(f"   - Memory usage: ~{self._estimate_memory_mb():.1f} MB")
            
        except Exception as e:
            print(f"Failed to initialize Transformer model: {e}")
            raise RuntimeError(f"Transformer model initialization failed: {e}")

    def _prepare_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """Prepare data for transformer training/prediction."""
        # Convert to device
        data = data.to(self.device)

        if truth_data is not None:
            truth_data = truth_data.to(self.device)
            return {
                'input_data': data,
                'target_data': truth_data,
                'mask': ~torch.isnan(data)
            }
        else:
            return {
                'input_data': data,
                'mask': ~torch.isnan(data)
            }

    def fit(self, train_data: torch.Tensor, truth_data: torch.Tensor,
            epochs: Optional[int] = None, batch_size: Optional[int] = None,
            patience: int = 10, min_delta: float = 1e-4) -> None:
        """
        Train the transformer model with enhanced optimizations.

        Args:
            train_data: Training data with missing values
            truth_data: Complete ground truth data
            epochs: Number of epochs (optional override)
            batch_size: Batch size (optional override)
            patience: Early stopping patience
            min_delta: Minimum change for early stopping
        """
        import time
        import torch.optim.lr_scheduler as lr_scheduler
        from torch.utils.checkpoint import checkpoint
        import gc

        # Validate input data
        if not self._validate_input_data(train_data) or not self._validate_input_data(truth_data):
            raise ValueError("Invalid input data format")

        # Initialize model if not already done
        if self.model is None:
            self._initialize_model()
        
        # Verify model parameters are ready for training
        self._verify_gradient_setup()

        # Use provided parameters or defaults
        epochs = epochs or self.epochs
        batch_size = batch_size or self.batch_size
        
        # Calculate optimal batch size if adaptive sizing is enabled
        if self.adaptive_batch_size:
            optimal_batch_size = self._calculate_optimal_batch_size(train_data.shape)
            if optimal_batch_size != batch_size:
                print(f"🔧 Adjusting batch size: {batch_size} → {optimal_batch_size}")
                batch_size = optimal_batch_size

        print(f"Training Transformer for {epochs} epochs...")
        print(f"   Training data shape: {train_data.shape}")
        print(f"   Missing values: {torch.isnan(train_data).sum().item()}")
        print(f"   Batch size: {batch_size}")

        # Record start time
        start_time = time.time()

        # Prepare training data
        train_set = self._prepare_data(train_data, truth_data)
        input_data = train_set['input_data']
        target_data = train_set['target_data']
        mask = train_set['mask']

        # Replace NaN values with zeros for training
        input_data_clean = torch.where(torch.isnan(input_data), torch.zeros_like(input_data), input_data)

        # Initialize scheduler and early stopping
        scheduler = lr_scheduler.ReduceLROnPlateau(self.optimizer, mode='min', factor=0.5, patience=5)
        best_loss = float('inf')
        epochs_no_improve = 0

        # Training loop
        self.model.train()
        self.training_history['loss'] = []

        try:
            for epoch in range(epochs):
                epoch_start = time.time()
                total_loss = 0.0

                # Create batches
                n_samples = input_data_clean.size(0)
                n_batches = (n_samples + batch_size - 1) // batch_size

                for batch_idx in range(n_batches):
                    start_idx = batch_idx * batch_size
                    end_idx = min(start_idx + batch_size, n_samples)

                    batch_input = input_data_clean[start_idx:end_idx]
                    batch_target = target_data[start_idx:end_idx]
                    batch_mask = mask[start_idx:end_idx]

                    # Forward pass with gradient checkpointing and mixed precision
                    self.optimizer.zero_grad()
                    
                    try:
                        # Ensure input tensors require gradients for proper backpropagation
                        batch_input = batch_input.requires_grad_(True)
                        
                        if self.use_mixed_precision:
                            # Mixed precision forward pass
                            with torch.amp.autocast('cuda'):
                                def checkpoint_fn(inputs):
                                    return self.model(inputs)
                                predictions = checkpoint(checkpoint_fn, batch_input)

                                # Calculate loss only on observed values
                                loss = self._calculate_masked_loss(predictions, batch_target, batch_mask)

                            # Ensure loss is scalar and has gradients
                            if not loss.requires_grad:
                                print(f"⚠️ Warning: Loss tensor does not require gradients at batch {batch_idx}")
                                self.optimizer.zero_grad()
                                continue

                            # Backward pass with gradient scaling
                            self.scaler.scale(loss).backward()

                            # Check for gradient issues before unscaling
                            has_gradients = any(p.grad is not None for p in self.model.parameters() if p.requires_grad)

                            if not has_gradients:
                                print(f"⚠️ Warning: No gradients found at batch {batch_idx}")
                                self.optimizer.zero_grad()
                                # Still need to update scaler even when skipping
                                self.scaler.update()
                                continue

                            # Unscale gradients for gradient clipping and finite check
                            self.scaler.unscale_(self.optimizer)

                            # Check for finite gradients after unscaling
                            has_finite_gradients = True
                            for p in self.model.parameters():
                                if p.requires_grad and p.grad is not None:
                                    if not torch.isfinite(p.grad).all():
                                        has_finite_gradients = False
                                        break

                            if not has_finite_gradients:
                                print(f"⚠️ Warning: Non-finite gradients detected at batch {batch_idx}, skipping step")
                                self.optimizer.zero_grad()
                                # CRITICAL: Must call step and update even when skipping to reset scaler state
                                self.scaler.step(self.optimizer)  # This will be a no-op due to inf/nan gradients
                                self.scaler.update()
                                continue

                            # Gradient clipping (only if gradients are finite)
                            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                            # Optimizer step with scaler
                            self.scaler.step(self.optimizer)
                            self.scaler.update()
                            
                        else:
                            # Standard precision training
                            def checkpoint_fn(inputs):
                                return self.model(inputs)
                            predictions = checkpoint(checkpoint_fn, batch_input)

                            # Calculate loss only on observed values
                            loss = self._calculate_masked_loss(predictions, batch_target, batch_mask)
                            
                            # Ensure loss is scalar and has gradients
                            if not loss.requires_grad:
                                print(f"⚠️ Warning: Loss tensor does not require gradients at batch {batch_idx}")
                                continue
                            
                            # Backward pass
                            loss.backward()
                            
                            # Check for gradient issues
                            has_gradients = any(p.grad is not None and torch.isfinite(p.grad).all() 
                                              for p in self.model.parameters() if p.requires_grad)
                            
                            if not has_gradients:
                                print(f"⚠️ Warning: No finite gradients found at batch {batch_idx}")
                                continue
                            
                            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                            self.optimizer.step()

                        total_loss += loss.item()
                        
                    except RuntimeError as e:
                        if "does not require grad" in str(e):
                            print(f"⚠️ Gradient error at batch {batch_idx}: {e}")
                            print(f"   Input shape: {batch_input.shape}, Target shape: {batch_target.shape}")
                            print(f"   Mask sum: {batch_mask.sum().item()}")
                            continue
                        else:
                            raise

                # Record epoch metrics
                # Handle case where no batches processed successfully
                avg_loss = total_loss / max(n_batches, 1) if n_batches > 0 else 0.0
                epoch_time = time.time() - epoch_start
                self.training_history['loss'].append(avg_loss)
                self.training_history['epoch_times'].append(epoch_time)
                
                # Track memory usage
                self._track_memory_usage(epoch, avg_loss)

                scheduler.step(avg_loss)

                # Early stopping check
                if avg_loss < best_loss - min_delta:
                    best_loss = avg_loss
                    epochs_no_improve = 0
                else:
                    epochs_no_improve += 1
                    if epochs_no_improve >= patience:
                        print(f"Early stopping at epoch {epoch+1}")
                        break

                if (epoch + 1) % 10 == 0 or epoch == 0:
                    print(f"   Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}, Time: {epoch_time:.2f}s, LR: {self.optimizer.param_groups[0]['lr']:.6f}")

            self.is_fitted = True

            # Record training time
            training_time = time.time() - start_time
            self.training_history['total_training_time'] = training_time

            print(f"Transformer training completed in {training_time:.2f} seconds!")
            print(f"   Final loss: {self.training_history['loss'][-1]:.6f}")

        except Exception as e:
            print(f"Training failed: {e}")
            raise

    def _calculate_masked_loss(self, predictions: torch.Tensor, targets: torch.Tensor,
                              mask: torch.Tensor) -> torch.Tensor:
        """Calculate loss only on observed values."""
        # Apply mask to both predictions and targets
        masked_predictions = predictions[mask]
        masked_targets = targets[mask]

        if masked_predictions.numel() == 0:
            # If no observed values, return zero loss connected to the computational graph
            # This ensures gradients can flow back to model parameters
            return torch.mean(predictions) * 0.0

        return self.criterion(masked_predictions, masked_targets)

    def predict(self, data: torch.Tensor) -> torch.Tensor:
        """
        Predict/impute missing values using the transformer.

        Args:
            data: Input data with missing values

        Returns:
            Imputed data tensor
        """
        if not self.is_fitted:
            raise RuntimeError("Model must be fitted before prediction")

        # Validate input data
        if not self._validate_input_data(data):
            raise ValueError("Invalid input data format")

        print(f"Predicting with Transformer...")
        print(f"   Input data shape: {data.shape}")

        # Prepare test data
        test_set = self._prepare_data(data)
        input_data = test_set['input_data']
        mask = test_set['mask']

        # Replace NaN values with zeros for prediction
        input_data_clean = torch.where(torch.isnan(input_data), torch.zeros_like(input_data), input_data)

        # Prediction with mixed precision support
        self.model.eval()
        with torch.no_grad():
            if self.use_mixed_precision:
                with torch.amp.autocast('cuda'):
                    predictions = self.model(input_data_clean)
            else:
                predictions = self.model(input_data_clean)

            # Combine original observed values with predictions for missing values
            result = torch.where(mask, input_data, predictions)

        print(f"Prediction completed")
        return result.cpu()

    def _estimate_parameters(self) -> int:
        """Estimate the number of model parameters."""
        if self.model is not None:
            return sum(p.numel() for p in self.model.parameters())

        # Rough estimation for transformer model
        attention_params = self.n_encoder_layers * (
            # Multi-head attention parameters
            4 * self.d_model * self.d_model +  # Q, K, V, O projections
            # Feed-forward parameters
            self.d_model * self.d_ff + self.d_ff * self.d_model
        )

        # Input/output embeddings and other components
        other_params = (
            self.n_features * self.d_model +  # Input embedding
            self.d_model * self.n_features +  # Output projection
            self.max_seq_len * self.d_model   # Positional encoding
        )

        return attention_params + other_params

    def _estimate_memory_mb(self) -> float:
        """Estimate memory usage in MB."""
        # Rough estimation based on model size and batch size
        param_memory = self._estimate_parameters() * 4 / (1024 * 1024)  # 4 bytes per float32
        activation_memory = (
            self.batch_size * self.sequence_len * self.d_model *
            self.n_encoder_layers * 4 / (1024 * 1024)
        )
        return param_memory + activation_memory

    def _calculate_optimal_batch_size(self, train_data_shape: Tuple[int, ...]) -> int:
        """Calculate optimal batch size for current GPU memory and model configuration."""
        try:
            from utils.memory_optimization import adaptive_batch_size_finder
            
            model_params = {
                'd_model': self.d_model,
                'n_layers': self.n_encoder_layers,
                'n_heads': self.n_heads,
                'memory_optimizations': {
                    'gradient_checkpointing': True,
                    'mixed_precision': self.use_mixed_precision
                }
            }
            
            optimal_batch_size = adaptive_batch_size_finder(
                train_data_shape, 
                model_params,
                start_batch_size=self.batch_size
            )
            
            return optimal_batch_size
            
        except ImportError:
            print("Memory optimization utilities not available, using default batch size")
            return self.batch_size
        except Exception as e:
            print(f"Error calculating optimal batch size: {e}")
            return self.batch_size

    def _track_memory_usage(self, epoch: int, loss: float) -> None:
        """Track memory usage during training."""
        if torch.cuda.is_available():
            current_memory = torch.cuda.memory_allocated() / (1024**3)  # GB
            peak_memory = torch.cuda.max_memory_allocated() / (1024**3)  # GB
            
            self.training_history.setdefault('memory_usage', []).append({
                'epoch': epoch,
                'current_gb': current_memory,
                'peak_gb': peak_memory,
                'loss': loss
            })
            
            if epoch % 10 == 0:
                print(f"   Memory: {current_memory:.2f}GB current, {peak_memory:.2f}GB peak")

    def generate_memory_report(self) -> str:
        """Generate comprehensive memory usage report."""
        try:
            from utils.memory_optimization import create_memory_report
            
            model_params = {
                'd_model': self.d_model,
                'n_layers': self.n_encoder_layers,
                'n_heads': self.n_heads,
                'memory_optimizations': {
                    'gradient_checkpointing': True,
                    'mixed_precision': getattr(self, 'use_mixed_precision', False)
                }
            }
            
            report = create_memory_report(
                (self.batch_size, self.sequence_len, self.n_features),
                model_params,
                self.batch_size
            )
            
            return report
            
        except ImportError:
            return "Memory optimization utilities not available for report generation"
        except Exception as e:
            return f"Error generating memory report: {e}"

    def fit_with_memory_optimization(self, train_data: torch.Tensor, truth_data: torch.Tensor, **kwargs) -> None:
        """Enhanced fit method with automatic memory optimization and emergency recovery."""
        
        # Generate pre-training memory report
        print("\n📊 Pre-training Memory Analysis:")
        report = self.generate_memory_report()
        print(report)
        
        # Use memory-efficient context if available
        try:
            from utils.memory_optimization import get_memory_optimizer
            memory_optimizer = get_memory_optimizer(
                enable_mixed_precision=self.use_mixed_precision,
                enable_monitoring=True
            )
            
            with memory_optimizer.memory_efficient_context():
                try:
                    self.fit(train_data, truth_data, **kwargs)
                    print("✅ Training completed successfully with memory optimizations")
                    
                except RuntimeError as e:
                    if "out of memory" in str(e):
                        print("💥 Implementing emergency memory recovery...")
                        self._emergency_fit(train_data, truth_data, **kwargs)
                    else:
                        raise
                        
        except ImportError:
            print("⚠️ Memory optimization context not available, using standard training")
            try:
                self.fit(train_data, truth_data, **kwargs)
            except RuntimeError as e:
                if "out of memory" in str(e):
                    print("💥 Implementing emergency memory recovery...")
                    self._emergency_fit(train_data, truth_data, **kwargs)
                else:
                    raise
        
        # Generate post-training memory summary
        print("\n📊 Post-training Memory Summary:")
        if torch.cuda.is_available():
            current_memory = torch.cuda.memory_allocated() / (1024**3)
            peak_memory = torch.cuda.max_memory_allocated() / (1024**3)
            print(f"   Final memory usage: {current_memory:.2f}GB current, {peak_memory:.2f}GB peak")
    
    def _emergency_fit(self, train_data: torch.Tensor, truth_data: torch.Tensor, **kwargs) -> None:
        """Emergency training with maximum memory optimization."""
        print("🚨 Emergency mode: Maximum memory optimization")
        
        # Reduce batch size drastically
        emergency_batch_size = max(1, kwargs.get('batch_size', self.batch_size) // 4)
        kwargs['batch_size'] = emergency_batch_size
        
        # Clear all memory
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        import gc
        gc.collect()
        
        # Force mixed precision
        original_mixed_precision = self.use_mixed_precision
        self.use_mixed_precision = True
        if self.scaler is None:
            self.scaler = torch.amp.GradScaler('cuda')
        
        print(f"   • Emergency batch size: {emergency_batch_size}")
        print(f"   • Forced mixed precision: True")
        
        try:
            # Retry training
            self.fit(train_data, truth_data, **kwargs)
            print("✅ Emergency training completed")
        finally:
            # Restore original settings
            self.use_mixed_precision = original_mixed_precision

    def get_attention_weights(self, data: torch.Tensor) -> Optional[np.ndarray]:
        """
        Extract attention weights for visualization.

        Args:
            data: Input data tensor

        Returns:
            Attention weights array or None if not available
        """
        if not self.is_fitted:
            print("⚠️ Model must be fitted before extracting attention weights")
            return None

        try:
            # This would require modification to store attention weights during forward pass
            # For now, return None and implement in future versions
            print("ℹ️ Attention weight extraction not yet implemented")
            return None
        except Exception as e:
            print(f"⚠️ Failed to extract attention weights: {e}")
            return None

    def get_model_complexity(self) -> Dict[str, Any]:
        """Get model complexity metrics."""
        return {
            'total_parameters': self._estimate_parameters(),
            'attention_heads': self.n_heads,
            'encoder_layers': self.n_encoder_layers,
            'model_dimension': self.d_model,
            'complexity_score': 4,  # Very high complexity
            'memory_mb': self._estimate_memory_mb(),
            'computational_cost': 'high',
            'performance_tier': 'highest'
        }
