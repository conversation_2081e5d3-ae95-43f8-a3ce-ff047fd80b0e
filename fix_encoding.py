#!/usr/bin/env python3
"""
Quick fix for encoding issues in memory optimization module
"""

import re

def fix_emoji_in_file(file_path):
    """Replace problematic emoji characters with ASCII equivalents"""
    
    replacements = {
        '🔍': '[SEARCH]',
        '💥': '[ERROR]',
        '🔄': '[RETRY]', 
        '📊': '[INFO]',
        '🧪': '[TEST]',
        '🧠': '[MEM]',
        '💻': '[SYS]',
        '🚀': '[GPU]',
        '✅': '[OK]',
        '⚠️': '[WARN]',
        '❌': '[FAIL]',
        '🔧': '[FIX]',
        '💡': '[TIP]',
        '🎉': '[SUCCESS]',
        '🧮': '[CALC]',
        '🧹': '[CLEAN]',
        '🤖': '[MODEL]',
        '💾': '[MEMORY]',
        '🖥️': '[SYSTEM]',
        '⏱️': '[TIME]',
        '📉': '[DOWN]',
        '📈': '[UP]',
        '🟡': '[MEDIUM]',
        '•': '*',
        '→': '>',
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Apply replacements
        for emoji, replacement in replacements.items():
            content = content.replace(emoji, replacement)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"[OK] Fixed encoding in {file_path}")
        return True
        
    except Exception as e:
        print(f"[FAIL] Error fixing {file_path}: {e}")
        return False

if __name__ == "__main__":
    files_to_fix = [
        "utils/memory_optimization.py"
    ]
    
    for file_path in files_to_fix:
        fix_emoji_in_file(file_path)
    
    print("[COMPLETE] Encoding fix completed")