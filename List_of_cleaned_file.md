# List of Files for Cleanup - Unrelated to Main Pipeline

## Overview
This document lists all files that are **not part of the core main pipeline** as documented in CLAUDE.md and README.md. These files can be safely archived, moved, or removed without affecting the main ML log prediction workflow.

## Core Main Pipeline Files (KEEP - DO NOT CLEAN)

### Essential Pipeline Components
```
main.py                     # Entry point with GUI workflow
data_handler.py            # LAS file operations and preprocessing  
ml_core.py                 # Model registry and training pipeline
config_handler.py          # User interfaces and configuration
reporting.py               # Visualization and analysis
enhanced_preprocessing.py  # Advanced preprocessing pipeline
```

### Model Implementations (KEEP)
```
models/
├── __init__.py
├── simple_autoencoder.py     # Neural network models
├── autoencoder.py
├── unet.py
├── neuralnet.py
└── advanced_models/
    ├── __init__.py
    ├── base_model.py
    ├── saits_model.py         # Advanced deep learning models
    ├── brits_model.py
    ├── enhanced_unet.py
    ├── mrnn_model.py
    ├── transformer_model.py
    └── utils/
        ├── __init__.py
        └── data_preparation.py
```

### Utility Modules (KEEP)
```
utils/
├── __init__.py
├── display_utils.py           # Cross-platform display
├── gpu_acceleration.py       # GPU optimization
├── xgboost_gpu_utils.py      # XGBoost GPU support
├── memory_optimization.py    # Memory management
├── performance_monitor.py    # Performance tracking
├── visualization_advanced.py # Advanced plots
├── hyperparameter_tuning.py  # Optimization
├── optimization.py           # General optimization
├── metrics.py                # Performance metrics
├── mixed_precision_utils.py  # Training optimization
└── gpu_fallback.py           # GPU fallback strategies
```

### Configuration & Data (KEEP)
```
Las/                       # Input LAS files
config/display_config.ini  # Display settings
requirements.txt           # Dependencies
CLAUDE.md                  # Project documentation
README.md                  # Main documentation
```

---

## Files Unrelated to Main Pipeline (SAFE TO CLEAN)

### 🗂️ **Archive Directories** (ENTIRE DIRECTORIES - MOVE TO SEPARATE ARCHIVE)

#### 1. `archives/` - Complete Directory
- **Purpose**: Historical code, experiments, and old implementations
- **Impact**: Zero - completely separate from main pipeline
- **Size**: Very large with multiple subdirectories
- **Recommendation**: Move entire directory to external archive

#### 2. `tutorial_results/` - Training Output Directory  
- **Purpose**: PyPOTS tutorial training outputs and tensorboard logs
- **Impact**: Zero - can be regenerated
- **Recommendation**: Delete or archive

#### 3. `example/` - Tutorial Directory
- **Purpose**: PyPOTS examples and tutorials
- **Impact**: Zero - not part of main pipeline
- **Recommendation**: Move to separate examples archive

#### 4. `plots/` - Generated Output Directory
- **Purpose**: Generated visualization outputs
- **Impact**: Zero - regenerated during pipeline execution
- **Recommendation**: Clean periodically

#### 5. `catboost_info/` - Generated Training Data
- **Purpose**: CatBoost training metadata and logs
- **Impact**: Zero - auto-generated during training
- **Recommendation**: Safe to delete (regenerates automatically)

#### 6. `docs/` - Legacy Documentation
- **Purpose**: Historical documentation and implementation notes
- **Impact**: Zero - replaced by current CLAUDE.md and README.md
- **Recommendation**: Archive for historical reference

#### 7. `tests/` - Test Files Directory
- **Purpose**: Various test and benchmark files
- **Impact**: Zero - development testing only
- **Recommendation**: Keep minimal essential tests, archive rest

### 📄 **Individual Files** (SAFE TO CLEAN)

#### Test & Debug Files
```
debug_autoencoder_evaluation.py    # Debug script
fix_autoencoder_evaluation.py      # Fix script  
fix_encoding.py                    # Encoding fix
simple_test.py                     # Basic test
simple_transformer_test.py        # Model test
tefn_visualization_fixed.py       # Visualization test
```

#### Utility Files (Non-Essential)
```
data_leakage_detector.py           # Optional validation tool
mlr_utils.py                       # Linear regression utilities (optional)
```

#### Documentation Files (Outdated)
```
codebase_structure.md              # Replaced by CLAUDE.md
ML_Categorize.md                   # Legacy categorization
TEFN_FIX_SUMMARY.md                # Historical fix notes
Transformer_Memory_Optimization.md # Implementation notes
CODEBASE_CLEANUP_SUMMARY.md       # Previous cleanup notes
Advanced_Preprocess_Stabilize.md   # Current strategy document (keep for now)
```

---

## Cleanup Recommendations

### **Phase 1: High-Impact Cleanup (Immediate)**
```bash
# Move large archive directories (saves most space)
mv archives/ ../archives_backup/
mv tutorial_results/ ../tutorial_results_backup/
mv example/ ../example_backup/

# Clean generated outputs (regeneratable)
rm -rf catboost_info/
rm -rf plots/*.png  # Keep directory, remove old plots
```

### **Phase 2: Medium-Impact Cleanup**
```bash
# Archive documentation
mkdir ../docs_archive/
mv docs/ ../docs_archive/
mv codebase_structure.md ../docs_archive/
mv ML_Categorize.md ../docs_archive/
mv TEFN_FIX_SUMMARY.md ../docs_archive/
mv Transformer_Memory_Optimization.md ../docs_archive/
mv CODEBASE_CLEANUP_SUMMARY.md ../docs_archive/

# Archive test files
mkdir ../tests_archive/
mv tests/ ../tests_archive/
```

### **Phase 3: Low-Impact Cleanup (Optional)**
```bash
# Remove individual debug/test files
rm debug_autoencoder_evaluation.py
rm fix_autoencoder_evaluation.py
rm fix_encoding.py
rm simple_test.py
rm simple_transformer_test.py
rm tefn_visualization_fixed.py
```

---

## Impact Assessment

### **Zero Impact Files** (100% Safe to Remove)
- All `archives/` content
- All `tutorial_results/` content
- All `example/` content  
- All `catboost_info/` content
- All old plot files in `plots/`
- All debug/test scripts

### **Minimal Impact Files** (95% Safe to Remove)
- Documentation files (replaced by current docs)
- Legacy test files
- Historical fix summaries

### **Keep for Reference** (May Have Some Value)
- `data_leakage_detector.py` (optional validation)
- `mlr_utils.py` (interpretable models)
- Current test files in `tests/` (minimal set)

---

## Storage Impact

### **Estimated Space Savings**
- `archives/`: ~500MB+ (largest directory)
- `tutorial_results/`: ~100MB+ (tensorboard logs)
- `example/`: ~50MB (notebooks and outputs)
- `docs/`: ~20MB (markdown files)
- `catboost_info/`: ~10MB (training logs)
- `plots/`: ~5MB (image files)

**Total Estimated Savings**: ~685MB+ (significant space reduction)

---

## Final File Structure After Cleanup

```
branch_2_gpu/
├── main.py                     # Core pipeline
├── data_handler.py            
├── ml_core.py                 
├── config_handler.py          
├── reporting.py               
├── enhanced_preprocessing.py   
├── requirements.txt           
├── CLAUDE.md                  
├── README.md                  
├── Advanced_Preprocess_Stabilize.md  # Current strategy
├── models/                    # All model implementations
├── utils/                     # All utility modules  
├── Las/                       # Input data
├── config/                    # Configuration files
└── plots/                     # Output plots (empty after cleanup)
```

This cleanup maintains 100% main pipeline functionality while removing ~685MB+ of non-essential files and significantly simplifying the codebase structure.