#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for Phase 1 Memory Optimization Implementation
Tests environment configuration and memory monitoring functionality.
"""

import os
import sys
import traceback

# Fix encoding for Windows console
if sys.platform == 'win32':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

def test_environment_configuration():
    """Test Phase 1 Step 1: Environment Configuration"""
    print("[TEST] Testing Phase 1 Step 1: Environment Configuration")
    print("="*60)
    
    # Test 1: Check if PYTORCH_CUDA_ALLOC_CONF is set correctly
    expected_value = 'expandable_segments:True'
    actual_value = os.environ.get('PYTORCH_CUDA_ALLOC_CONF', 'Not Set')
    
    print(f"Environment Variable Test:")
    print(f"   Expected: PYTORCH_CUDA_ALLOC_CONF={expected_value}")
    print(f"   Actual:   PYTORCH_CUDA_ALLOC_CONF={actual_value}")
    
    if actual_value == expected_value:
        print("   ✅ PASS: Environment configuration is correct")
        env_test_passed = True
    else:
        print("   ❌ FAIL: Environment configuration is incorrect")
        print("   🔧 Setting environment variable for this test...")
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = expected_value
        env_test_passed = False
    
    return env_test_passed

def test_memory_optimization_import():
    """Test Phase 1 Step 2: Memory Optimization Import"""
    print("\n🧪 Testing Phase 1 Step 2: Memory Optimization Import")
    print("="*60)
    
    try:
        from utils.memory_optimization import get_memory_optimizer
        print("   ✅ PASS: Memory optimization module imported successfully")
        
        # Test memory optimizer initialization
        memory_optimizer = get_memory_optimizer(
            enable_mixed_precision=True,
            enable_monitoring=True
        )
        print("   ✅ PASS: Memory optimizer initialized successfully")
        
        return True, memory_optimizer
    
    except ImportError as e:
        print(f"   ❌ FAIL: Failed to import memory optimization: {e}")
        return False, None
    except Exception as e:
        print(f"   ❌ FAIL: Failed to initialize memory optimizer: {e}")
        return False, None

def test_pytorch_with_optimization():
    """Test PyTorch functionality with optimization"""
    print("\n🧪 Testing PyTorch with Memory Optimization")
    print("="*60)
    
    try:
        import torch
        print(f"   ✅ PyTorch imported successfully")
        print(f"   • PyTorch version: {torch.__version__}")
        print(f"   • CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"   • CUDA version: {torch.version.cuda}")
            print(f"   • Device count: {torch.cuda.device_count()}")
            print(f"   • Current device: {torch.cuda.current_device()}")
            print(f"   • Device name: {torch.cuda.get_device_name()}")
            
            # Test memory allocation
            try:
                test_tensor = torch.randn(100, 100, device='cuda')
                print(f"   ✅ Test CUDA tensor created successfully")
                
                # Check memory usage
                allocated = torch.cuda.memory_allocated() / (1024**2)  # MB
                reserved = torch.cuda.memory_reserved() / (1024**2)    # MB
                print(f"   • GPU memory allocated: {allocated:.2f} MB")
                print(f"   • GPU memory reserved: {reserved:.2f} MB")
                
                # Clean up
                del test_tensor
                torch.cuda.empty_cache()
                print(f"   ✅ Memory cleanup completed")
                
            except Exception as e:
                print(f"   ⚠️ WARNING: CUDA tensor test failed: {e}")
        
        return True
    
    except Exception as e:
        print(f"   ❌ FAIL: PyTorch test failed: {e}")
        return False

def test_memory_optimizer_functionality(memory_optimizer):
    """Test memory optimizer functionality"""
    print("\n🧪 Testing Memory Optimizer Functionality")
    print("="*60)
    
    if memory_optimizer is None:
        print("   ⚠️ SKIP: Memory optimizer not available")
        return False
    
    try:
        # Test 1: Memory info retrieval
        memory_info = memory_optimizer.get_memory_info()
        print("   ✅ PASS: Memory info retrieved successfully")
        print(f"   • System RAM total: {memory_info.get('system_memory_total_gb', 'N/A'):.1f} GB")
        print(f"   • System RAM available: {memory_info.get('system_memory_available_gb', 'N/A'):.1f} GB")
        
        if 'gpu_memory_total_gb' in memory_info:
            print(f"   • GPU memory total: {memory_info['gpu_memory_total_gb']:.1f} GB")
            print(f"   • GPU memory free: {memory_info['gpu_memory_free_gb']:.1f} GB")
        
        # Test 2: Memory context manager
        print("\n   Testing memory efficient context manager...")
        with memory_optimizer.memory_efficient_context():
            print("   ✅ PASS: Memory context manager works")
        
        # Test 3: Memory clearing
        print("\n   Testing memory clearing...")
        memory_optimizer.clear_memory()
        print("   ✅ PASS: Memory clearing works")
        
        return True
    
    except Exception as e:
        print(f"   ❌ FAIL: Memory optimizer functionality test failed: {e}")
        traceback.print_exc()
        return False

def test_integration_with_main():
    """Test integration with main.py"""
    print("\n🧪 Testing Integration with Main Pipeline")
    print("="*60)
    
    try:
        # Test importing main with optimizations
        print("   Testing main.py import with optimizations...")
        
        # Save current environment
        original_env = os.environ.get('PYTORCH_CUDA_ALLOC_CONF', None)
        
        # Test import
        import main
        print("   ✅ PASS: main.py imported successfully with optimizations")
        
        # Check if optimization flags are available
        if hasattr(main, 'MEMORY_OPTIMIZATION_AVAILABLE'):
            print(f"   • Memory optimization available: {main.MEMORY_OPTIMIZATION_AVAILABLE}")
            if hasattr(main, 'memory_optimizer') and main.memory_optimizer:
                print("   ✅ PASS: Memory optimizer initialized in main.py")
            else:
                print("   ⚠️ WARNING: Memory optimizer not initialized in main.py")
        else:
            print("   ⚠️ WARNING: Memory optimization flags not found in main.py")
        
        return True
    
    except Exception as e:
        print(f"   ❌ FAIL: Main integration test failed: {e}")
        traceback.print_exc()
        return False

def run_comprehensive_test():
    """Run comprehensive Phase 1 optimization test"""
    print("🚀 COMPREHENSIVE PHASE 1 OPTIMIZATION TEST")
    print("="*80)
    print("Testing transformer memory optimization Phase 1 implementation")
    print("="*80)
    
    # Test results
    test_results = []
    
    # Test 1: Environment Configuration
    env_test = test_environment_configuration()
    test_results.append(("Environment Configuration", env_test))
    
    # Test 2: Memory Optimization Import
    import_test, memory_optimizer = test_memory_optimization_import()
    test_results.append(("Memory Optimization Import", import_test))
    
    # Test 3: PyTorch with Optimization
    pytorch_test = test_pytorch_with_optimization()
    test_results.append(("PyTorch with Optimization", pytorch_test))
    
    # Test 4: Memory Optimizer Functionality
    functionality_test = test_memory_optimizer_functionality(memory_optimizer)
    test_results.append(("Memory Optimizer Functionality", functionality_test))
    
    # Test 5: Integration Test
    integration_test = test_integration_with_main()
    test_results.append(("Main Pipeline Integration", integration_test))
    
    # Summary Report
    print("\n" + "="*80)
    print("📊 TEST SUMMARY REPORT")
    print("="*80)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:30} | {status}")
        if result:
            passed_tests += 1
    
    print(f"\nOverall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED - Phase 1 optimization implemented successfully!")
        success_percentage = 100
    else:
        success_percentage = (passed_tests / total_tests) * 100
        print(f"⚠️ {total_tests - passed_tests} tests failed - {success_percentage:.1f}% success rate")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS:")
    if success_percentage >= 80:
        print("   • Phase 1 implementation is successful")
        print("   • Ready to proceed to Phase 2 (Mixed Precision)")
        print("   • Monitor memory usage during actual training")
    else:
        print("   • Review failed tests and fix issues")
        print("   • Check dependencies and imports")
        print("   • Verify environment configuration")
    
    print(f"\n🏁 Phase 1 optimization test completed with {success_percentage:.1f}% success rate")
    
    return success_percentage >= 80

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)