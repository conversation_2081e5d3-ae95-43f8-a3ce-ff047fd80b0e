"""
GPU Memory Optimization Utilities for ML Log Prediction
Provides comprehensive memory management for large dataset processing.
"""

import os
import torch
import numpy as np
import gc
import math
from typing import Dict, List, Optional, Tuple, Any, Union
import warnings
from contextlib import contextmanager
import psutil

# Import existing GPU utilities
try:
    from .gpu_fallback import get_fallback_manager, safe_cuda_empty_cache
    from .performance_monitor import get_performance_monitor
    GPU_UTILS_AVAILABLE = True
except ImportError:
    print("⚠️ GPU utilities not available for memory optimization")
    GPU_UTILS_AVAILABLE = False
    get_fallback_manager = lambda: None
    safe_cuda_empty_cache = lambda: None
    get_performance_monitor = lambda: None


class MemoryOptimizer:
    """
    Comprehensive memory optimization manager for GPU operations.
    Handles batch processing, memory monitoring, and automatic fallback.
    """
    
    def __init__(self, enable_mixed_precision=True, enable_monitoring=True):
        """
        Initialize memory optimizer.
        
        Args:
            enable_mixed_precision: Whether to use automatic mixed precision
            enable_monitoring: Whether to enable memory monitoring
        """
        self.enable_mixed_precision = enable_mixed_precision
        self.enable_monitoring = enable_monitoring
        
        # Initialize managers
        self.fallback_manager = get_fallback_manager() if GPU_UTILS_AVAILABLE else None
        self.performance_monitor = get_performance_monitor() if GPU_UTILS_AVAILABLE else None
        
        # Memory tracking
        self.memory_stats = {
            'peak_allocated': 0,
            'peak_reserved': 0,
            'oom_count': 0,
            'fallback_count': 0,
            'batch_count': 0
        }
        
        # Configure environment for better memory management
        self._configure_environment()
        
        print("🧠 Memory Optimizer initialized")
        if self.enable_mixed_precision:
            print("   • Mixed precision enabled")
        if self.enable_monitoring:
            print("   • Memory monitoring enabled")
    
    def _configure_environment(self):
        """Configure environment variables for optimal memory usage."""
        # Set PyTorch CUDA memory allocator configuration
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
        
        # Additional memory optimization settings
        if torch.cuda.is_available():
            # Enable memory pool for better allocation
            torch.cuda.empty_cache()
            
            # Set memory fraction if not already set
            if not hasattr(torch.cuda, '_memory_fraction_set'):
                try:
                    torch.cuda.set_per_process_memory_fraction(0.9)  # Use 90% of GPU memory
                    torch.cuda._memory_fraction_set = True
                except:
                    pass  # Ignore if already set
        
        print("🔧 Environment configured for memory optimization")
    
    def estimate_memory_requirements(self, data_shape: Tuple[int, ...], 
                                   model_params: Dict[str, Any]) -> Dict[str, float]:
        """
        Estimate memory requirements for given data and model.
        
        Args:
            data_shape: Shape of input data (batch, sequence, features)
            model_params: Model parameters for estimation
            
        Returns:
            Dictionary with memory estimates in MB
        """
        batch_size, seq_len, n_features = data_shape
        
        # Model parameter estimation
        d_model = model_params.get('d_model', 256)
        n_layers = model_params.get('n_layers', 2)
        n_heads = model_params.get('n_heads', 4)
        
        # Estimate model parameters
        # Attention layers: Q, K, V projections + output projection
        attention_params = n_layers * (4 * d_model * d_model + d_model * 4)
        # Feed-forward layers
        ff_params = n_layers * (d_model * d_model * 4 + d_model * 4 * d_model)
        # Embeddings and other parameters
        other_params = d_model * n_features + d_model * seq_len
        
        total_params = attention_params + ff_params + other_params
        model_memory_mb = total_params * 4 / (1024 * 1024)  # 4 bytes per float32
        
        # Activation memory estimation
        # Attention activations: batch_size * n_heads * seq_len * seq_len
        attention_memory = batch_size * n_heads * seq_len * seq_len * n_layers * 4
        # Feature activations: batch_size * seq_len * d_model
        feature_memory = batch_size * seq_len * d_model * n_layers * 4
        # Input/output memory
        io_memory = batch_size * seq_len * n_features * 4 * 2  # input + output
        
        activation_memory_mb = (attention_memory + feature_memory + io_memory) / (1024 * 1024)
        
        # Gradient memory (same as parameters)
        gradient_memory_mb = model_memory_mb
        
        # Optimizer state (Adam: 2x parameters)
        optimizer_memory_mb = model_memory_mb * 2
        
        # Total memory with safety margin
        total_memory_mb = (model_memory_mb + activation_memory_mb + 
                          gradient_memory_mb + optimizer_memory_mb) * 1.2
        
        return {
            'model_memory_mb': model_memory_mb,
            'activation_memory_mb': activation_memory_mb,
            'gradient_memory_mb': gradient_memory_mb,
            'optimizer_memory_mb': optimizer_memory_mb,
            'total_memory_mb': total_memory_mb,
            'estimated_parameters': total_params
        }
    
    def calculate_optimal_batch_size(self, data_shape: Tuple[int, ...], 
                                   model_params: Dict[str, Any],
                                   available_memory_mb: Optional[float] = None) -> int:
        """
        Calculate optimal batch size for given memory constraints.
        
        Args:
            data_shape: Shape of input data (total_samples, sequence, features)
            model_params: Model parameters
            available_memory_mb: Available GPU memory in MB (auto-detect if None)
            
        Returns:
            Optimal batch size
        """
        total_samples, seq_len, n_features = data_shape
        
        # Get available memory
        if available_memory_mb is None:
            if torch.cuda.is_available():
                total_memory = torch.cuda.get_device_properties(0).total_memory
                allocated_memory = torch.cuda.memory_allocated()
                available_memory_mb = (total_memory - allocated_memory) / (1024 * 1024)
                # Use 80% of available memory for safety
                available_memory_mb *= 0.8
            else:
                # Use system RAM estimation for CPU
                available_memory_mb = psutil.virtual_memory().available / (1024 * 1024) * 0.5
        
        # Start with a reasonable batch size and adjust
        test_batch_size = min(32, total_samples)
        
        while test_batch_size > 1:
            test_shape = (test_batch_size, seq_len, n_features)
            memory_est = self.estimate_memory_requirements(test_shape, model_params)
            
            if memory_est['total_memory_mb'] <= available_memory_mb:
                break
            
            test_batch_size = max(1, test_batch_size // 2)
        
        # Ensure we can process at least one sample
        optimal_batch_size = max(1, test_batch_size)
        
        print(f"🧮 Calculated optimal batch size: {optimal_batch_size}")
        print(f"   • Available memory: {available_memory_mb:.1f} MB")
        print(f"   • Estimated usage: {self.estimate_memory_requirements((optimal_batch_size, seq_len, n_features), model_params)['total_memory_mb']:.1f} MB")
        
        return optimal_batch_size
    
    def get_memory_info(self) -> Dict[str, float]:
        """Get current memory information."""
        info = {
            'system_memory_total_gb': psutil.virtual_memory().total / (1024**3),
            'system_memory_available_gb': psutil.virtual_memory().available / (1024**3),
            'system_memory_percent': psutil.virtual_memory().percent
        }
        
        if torch.cuda.is_available():
            try:
                info.update({
                    'gpu_memory_total_gb': torch.cuda.get_device_properties(0).total_memory / (1024**3),
                    'gpu_memory_allocated_gb': torch.cuda.memory_allocated() / (1024**3),
                    'gpu_memory_reserved_gb': torch.cuda.memory_reserved() / (1024**3),
                    'gpu_memory_free_gb': (torch.cuda.get_device_properties(0).total_memory - 
                                         torch.cuda.memory_allocated()) / (1024**3)
                })
            except Exception as e:
                print(f"⚠️ Could not get GPU memory info: {e}")
        
        return info
    
    def print_memory_status(self):
        """Print current memory status."""
        info = self.get_memory_info()
        
        print("\n" + "="*50)
        print("🧠 MEMORY STATUS")
        print("="*50)
        
        print(f"💻 System Memory:")
        print(f"   • Total: {info['system_memory_total_gb']:.1f} GB")
        print(f"   • Available: {info['system_memory_available_gb']:.1f} GB")
        print(f"   • Usage: {info['system_memory_percent']:.1f}%")
        
        if 'gpu_memory_total_gb' in info:
            print(f"\n🚀 GPU Memory:")
            print(f"   • Total: {info['gpu_memory_total_gb']:.1f} GB")
            print(f"   • Allocated: {info['gpu_memory_allocated_gb']:.1f} GB")
            print(f"   • Reserved: {info['gpu_memory_reserved_gb']:.1f} GB")
            print(f"   • Free: {info['gpu_memory_free_gb']:.1f} GB")
        
        print("="*50)
    
    @contextmanager
    def memory_efficient_context(self, clear_cache_before=True, clear_cache_after=True):
        """
        Context manager for memory-efficient operations.
        
        Args:
            clear_cache_before: Whether to clear cache before operation
            clear_cache_after: Whether to clear cache after operation
        """
        if clear_cache_before:
            self.clear_memory()
        
        # Track memory before
        initial_memory = self.get_memory_info()
        
        try:
            yield
        finally:
            if clear_cache_after:
                self.clear_memory()
            
            # Track memory after
            final_memory = self.get_memory_info()
            
            # Update peak statistics
            if torch.cuda.is_available():
                try:
                    self.memory_stats['peak_allocated'] = max(
                        self.memory_stats['peak_allocated'],
                        torch.cuda.max_memory_allocated()
                    )
                    self.memory_stats['peak_reserved'] = max(
                        self.memory_stats['peak_reserved'],
                        torch.cuda.max_memory_reserved()
                    )
                except:
                    pass
    
    def clear_memory(self):
        """Comprehensive memory clearing."""
        # Python garbage collection
        gc.collect()
        
        # PyTorch cache clearing
        if torch.cuda.is_available():
            safe_cuda_empty_cache()
            
            # Reset peak memory stats
            try:
                torch.cuda.reset_peak_memory_stats()
            except:
                pass
        
        print("🧹 Memory cleared")


# Global memory optimizer instance
_global_memory_optimizer = None

def get_memory_optimizer(enable_mixed_precision: bool = True, 
                        enable_monitoring: bool = True) -> MemoryOptimizer:
    """Get the global memory optimizer instance with configuration."""
    global _global_memory_optimizer
    if _global_memory_optimizer is None:
        _global_memory_optimizer = MemoryOptimizer(
            enable_mixed_precision=enable_mixed_precision,
            enable_monitoring=enable_monitoring
        )
    return _global_memory_optimizer

def reset_memory_optimizer():
    """Reset the global memory optimizer (useful for testing)."""
    global _global_memory_optimizer
    _global_memory_optimizer = None

def print_comprehensive_memory_report(data_shape: Tuple[int, ...], 
                                    model_params: Dict[str, Any],
                                    batch_size: int):
    """Print a comprehensive memory analysis report."""
    report = create_memory_report(data_shape, model_params, batch_size)
    print(report)
    
    # Also print fallback report if available
    try:
        from .gpu_fallback import print_fallback_report
        print_fallback_report()
    except ImportError:
        pass

def estimate_batch_memory_usage(batch_size: int, sequence_len: int, 
                               n_features: int, model_params: Dict[str, Any]) -> float:
    """
    Quick estimation of memory usage for a batch.
    
    Returns:
        Estimated memory usage in MB
    """
    optimizer = get_memory_optimizer()
    return optimizer.estimate_memory_requirements(
        (batch_size, sequence_len, n_features), model_params
    )['total_memory_mb']

def get_optimal_batch_size(total_samples: int, sequence_len: int,
                          n_features: int, model_params: Dict[str, Any],
                          target_memory_usage: float = 0.8) -> int:
    """
    Get optimal batch size for given data dimensions with memory target.

    Args:
        total_samples: Total number of samples
        sequence_len: Sequence length
        n_features: Number of features
        model_params: Model parameters
        target_memory_usage: Target GPU memory usage (0.0-1.0)
    
    Returns:
        Optimal batch size
    """
    optimizer = get_memory_optimizer()
    
    # Get available memory
    memory_info = optimizer.get_memory_info()
    if 'gpu_memory_free_gb' in memory_info:
        available_memory_mb = memory_info['gpu_memory_free_gb'] * 1024 * target_memory_usage
    else:
        available_memory_mb = memory_info['system_memory_available_gb'] * 1024 * 0.3
    
    return optimizer.calculate_optimal_batch_size(
        (total_samples, sequence_len, n_features), model_params, available_memory_mb
    )

def adaptive_batch_size_finder(data_shape: Tuple[int, ...], model_params: Dict[str, Any],
                              start_batch_size: int = 32, min_batch_size: int = 1) -> int:
    """
    Adaptively find the largest batch size that fits in memory.

    Args:
        data_shape: Shape of input data (total_samples, sequence, features)
        model_params: Model parameters for estimation
        start_batch_size: Starting batch size to test
        min_batch_size: Minimum acceptable batch size

    Returns:
        Largest working batch size
    """
    total_samples, seq_len, n_features = data_shape
    optimizer = get_memory_optimizer()

    # Get available memory
    memory_info = optimizer.get_memory_info()
    if 'gpu_memory_free_gb' in memory_info:
        available_memory_mb = memory_info['gpu_memory_free_gb'] * 1024 * 0.8  # 80% safety margin
    else:
        available_memory_mb = memory_info['system_memory_available_gb'] * 1024 * 0.3  # 30% for CPU

    print(f"🔍 Adaptive batch size finder - Available memory: {available_memory_mb:.0f} MB")

    # Binary search for optimal batch size
    low, high = min_batch_size, min(start_batch_size, total_samples)
    best_batch_size = min_batch_size

    while low <= high:
        mid = (low + high) // 2
        test_shape = (mid, seq_len, n_features)

        memory_est = optimizer.estimate_memory_requirements(test_shape, model_params)
        estimated_usage = memory_est['total_memory_mb']

        print(f"   Testing batch size {mid}: {estimated_usage:.0f} MB estimated")

        if estimated_usage <= available_memory_mb:
            best_batch_size = mid
            low = mid + 1
        else:
            high = mid - 1

    print(f"✅ Optimal batch size found: {best_batch_size}")
    return best_batch_size

def monitor_memory_during_prediction(func):
    """
    Decorator to monitor memory usage during prediction with OOM recovery.

    Args:
        func: Function to monitor

    Returns:
        Wrapped function with memory monitoring and automatic recovery
    """
    def wrapper(*args, **kwargs):
        optimizer = get_memory_optimizer()

        print("🔍 Starting memory monitoring for prediction...")
        initial_memory = optimizer.get_memory_info()

        try:
            with optimizer.memory_efficient_context():
                result = func(*args, **kwargs)

            final_memory = optimizer.get_memory_info()

            # Calculate memory usage
            if 'gpu_memory_allocated_gb' in initial_memory and 'gpu_memory_allocated_gb' in final_memory:
                memory_delta = final_memory['gpu_memory_allocated_gb'] - initial_memory['gpu_memory_allocated_gb']
                print(f"📊 GPU memory change: {memory_delta:+.2f} GB")

            return result

        except RuntimeError as e:
            if "out of memory" in str(e):
                print(f"💥 OOM detected during prediction: {e}")
                print("🔄 Attempting automatic recovery...")
                
                # Emergency memory cleanup
                optimizer.clear_memory()
                
                # Try GPU fallback utilities if available
                try:
                    from .gpu_fallback import emergency_memory_recovery
                    emergency_memory_recovery()
                except ImportError:
                    pass
                
                # Retry the function once with reduced parameters
                print("🔄 Retrying with reduced batch size...")
                
                # Try to modify kwargs to reduce memory usage
                if 'batch_size' in kwargs:
                    kwargs['batch_size'] = max(1, kwargs['batch_size'] // 2)
                    print(f"   Reduced batch size to {kwargs['batch_size']}")
                
                try:
                    result = func(*args, **kwargs)
                    print("✅ Retry successful")
                    return result
                except Exception as retry_e:
                    print(f"❌ Retry failed: {retry_e}")
                    raise retry_e
            else:
                raise
        except Exception as e:
            print(f"❌ Memory monitoring detected error: {e}")
            # Try to recover memory
            optimizer.clear_memory()
            raise

    return wrapper

def estimate_processing_time(data_shape: Tuple[int, ...], model_params: Dict[str, Any],
                           batch_size: int) -> Dict[str, float]:
    """
    Estimate processing time for given configuration.

    Args:
        data_shape: Shape of input data (total_samples, sequence, features)
        model_params: Model parameters
        batch_size: Batch size to use

    Returns:
        Dictionary with time estimates
    """
    total_samples, seq_len, n_features = data_shape

    # Rough time estimation based on model complexity
    d_model = model_params.get('d_model', 256)
    n_layers = model_params.get('n_layers', 2)
    n_heads = model_params.get('n_heads', 4)

    # Base time per sample (in seconds) - rough estimation
    complexity_factor = (d_model * n_layers * n_heads) / (256 * 2 * 4)  # Normalized to baseline
    base_time_per_sample = 0.001 * complexity_factor  # 1ms per sample for baseline

    # Adjust for sequence length
    sequence_factor = seq_len / 64  # Normalized to 64-length sequences
    time_per_sample = base_time_per_sample * sequence_factor

    # Calculate batch processing time
    num_batches = math.ceil(total_samples / batch_size)
    batch_overhead = 0.1  # 100ms overhead per batch

    total_time = (total_samples * time_per_sample) + (num_batches * batch_overhead)

    return {
        'estimated_total_time_seconds': total_time,
        'estimated_time_per_sample_ms': time_per_sample * 1000,
        'estimated_batches': num_batches,
        'estimated_batch_time_seconds': total_time / num_batches if num_batches > 0 else 0
    }

def create_memory_report(data_shape: Tuple[int, ...], model_params: Dict[str, Any],
                        batch_size: int, include_fallback_info: bool = True) -> str:
    """
    Create a comprehensive memory usage report with fallback analysis.

    Args:
        data_shape: Shape of input data
        model_params: Model parameters
        batch_size: Batch size to analyze
        include_fallback_info: Whether to include GPU fallback information

    Returns:
        Formatted memory report string
    """
    optimizer = get_memory_optimizer()
    total_samples, seq_len, n_features = data_shape

    # Get memory estimates
    memory_est = optimizer.estimate_memory_requirements((batch_size, seq_len, n_features), model_params)
    time_est = estimate_processing_time(data_shape, model_params, batch_size)
    current_memory = optimizer.get_memory_info()

    report = []
    report.append("="*60)
    report.append("🧠 MEMORY OPTIMIZATION REPORT")
    report.append("="*60)

    # Dataset information
    report.append(f"📊 Dataset Information:")
    report.append(f"   • Total samples: {total_samples:,}")
    report.append(f"   • Sequence length: {seq_len}")
    report.append(f"   • Features: {n_features}")
    report.append(f"   • Data size: {total_samples * seq_len * n_features * 4 / (1024**2):.1f} MB")

    # Model information
    report.append(f"\n🤖 Model Configuration:")
    report.append(f"   • Model dimension: {model_params.get('d_model', 256)}")
    report.append(f"   • Layers: {model_params.get('n_layers', 2)}")
    report.append(f"   • Attention heads: {model_params.get('n_heads', 4)}")
    report.append(f"   • Estimated parameters: {memory_est['estimated_parameters']:,}")
    
    # Memory optimization features
    if 'memory_optimizations' in model_params:
        opts = model_params['memory_optimizations']
        report.append(f"\n🔧 Memory Optimizations:")
        report.append(f"   • Gradient checkpointing: {'✅' if opts.get('gradient_checkpointing') else '❌'}")
        report.append(f"   • Mixed precision: {'✅' if opts.get('mixed_precision') else '❌'}")
        report.append(f"   • Adaptive batch size: {'✅' if opts.get('adaptive_batch_size') else '❌'}")
        report.append(f"   • Memory efficient mode: {'✅' if opts.get('memory_efficient_mode') else '❌'}")

    # Memory estimates
    report.append(f"\n💾 Memory Estimates (per batch of {batch_size}):")
    report.append(f"   • Model memory: {memory_est['model_memory_mb']:.1f} MB")
    report.append(f"   • Activation memory: {memory_est['activation_memory_mb']:.1f} MB")
    report.append(f"   • Gradient memory: {memory_est['gradient_memory_mb']:.1f} MB")
    report.append(f"   • Optimizer memory: {memory_est['optimizer_memory_mb']:.1f} MB")
    report.append(f"   • Total estimated: {memory_est['total_memory_mb']:.1f} MB")

    # Current system status
    report.append(f"\n🖥️ Current System Status:")
    if 'gpu_memory_total_gb' in current_memory:
        report.append(f"   • GPU total: {current_memory['gpu_memory_total_gb']:.1f} GB")
        report.append(f"   • GPU allocated: {current_memory['gpu_memory_allocated_gb']:.1f} GB")
        report.append(f"   • GPU free: {current_memory['gpu_memory_free_gb']:.1f} GB")
        
        # Memory pressure analysis
        allocated_pct = (current_memory['gpu_memory_allocated_gb'] / current_memory['gpu_memory_total_gb']) * 100
        if allocated_pct > 80:
            report.append(f"   ⚠️ High memory usage: {allocated_pct:.1f}%")
        elif allocated_pct > 60:
            report.append(f"   🟡 Moderate memory usage: {allocated_pct:.1f}%")
        else:
            report.append(f"   ✅ Low memory usage: {allocated_pct:.1f}%")
    
    report.append(f"   • System RAM total: {current_memory['system_memory_total_gb']:.1f} GB")
    report.append(f"   • System RAM available: {current_memory['system_memory_available_gb']:.1f} GB")

    # GPU Fallback information
    if include_fallback_info:
        try:
            from .gpu_fallback import get_fallback_manager, check_memory_pressure
            fallback_manager = get_fallback_manager()
            fallback_summary = fallback_manager.get_fallback_summary()
            memory_pressure = check_memory_pressure()
            
            report.append(f"\n🔄 GPU Fallback Status:")
            report.append(f"   • Total fallbacks: {fallback_summary['total_fallbacks']}")
            report.append(f"   • OOM events: {fallback_summary['oom_count']}")
            report.append(f"   • Memory pressure: {memory_pressure['memory_pressure'].upper()}")
            
        except ImportError:
            report.append(f"\n🔄 GPU Fallback: Not available")

    # Processing estimates
    report.append(f"\n⏱️ Processing Estimates:")
    report.append(f"   • Number of batches: {time_est['estimated_batches']}")
    report.append(f"   • Estimated total time: {time_est['estimated_total_time_seconds']:.1f} seconds")
    report.append(f"   • Time per batch: {time_est['estimated_batch_time_seconds']:.2f} seconds")
    report.append(f"   • Time per sample: {time_est['estimated_time_per_sample_ms']:.2f} ms")

    # Smart recommendations
    report.append(f"\n💡 Smart Recommendations:")
    
    # Memory-based recommendations
    gpu_free_mb = current_memory.get('gpu_memory_free_gb', 0) * 1024
    if memory_est['total_memory_mb'] > gpu_free_mb:
        report.append(f"   ⚠️ Estimated memory usage ({memory_est['total_memory_mb']:.0f} MB) exceeds available GPU memory ({gpu_free_mb:.0f} MB)")
        
        # Calculate suggested batch size
        if gpu_free_mb > 0:
            suggested_batch = max(1, int(batch_size * (gpu_free_mb * 0.8) / memory_est['total_memory_mb']))
            report.append(f"   📉 Suggested batch size: {suggested_batch} (reduction from {batch_size})")
        
        report.append(f"   🔧 Enable gradient checkpointing to reduce memory by ~40%")
        report.append(f"   🔧 Enable mixed precision to reduce memory by ~30%")
        report.append(f"   🔄 Enable CPU fallback for automatic recovery")
    else:
        report.append(f"   ✅ Memory usage should fit comfortably in GPU memory")
        
        # Suggest increasing batch size if there's room
        if gpu_free_mb > memory_est['total_memory_mb'] * 2:
            max_batch = int(batch_size * (gpu_free_mb * 0.8) / memory_est['total_memory_mb'])
            report.append(f"   📈 Could increase batch size to ~{max_batch} for better performance")

    # Time-based recommendations
    if time_est['estimated_total_time_seconds'] > 300:  # 5 minutes
        report.append(f"   ⏱️ Long processing time expected ({time_est['estimated_total_time_seconds']:.0f}s)")
        report.append(f"   💡 Consider increasing batch size if memory allows")
        report.append(f"   📊 Enable progress monitoring")
    
    # Optimization recommendations
    if not model_params.get('memory_optimizations', {}).get('gradient_checkpointing'):
        report.append(f"   🚀 Enable gradient checkpointing for significant memory savings")
    
    if not model_params.get('memory_optimizations', {}).get('mixed_precision'):
        report.append(f"   🚀 Enable mixed precision for faster training and lower memory usage")

    report.append("="*60)

    return "\n".join(report)
